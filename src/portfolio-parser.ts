import {PortfolioEntry} from './types';

export class PortfolioParser {
    static parsePortfolioTable(tableText: string): PortfolioEntry[] {
        console.log('parsePortfolioTable: start');
        console.log('Input tableText:', tableText);

        const lines = tableText.split('\n')
            .map(line => line.trim())
            .filter(line => line);
        console.log(`Parsed lines (${lines.length}):`, lines);

        // Find header line with dates
        let headerIndex = -1;
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('Account') && lines[i].includes('Dec') && lines[i].includes('2023')) {
                headerIndex = i;
                console.log(`Header found at index ${headerIndex}:`, lines[i]);
                break;
            }
        }

        if (headerIndex === -1) {
            console.log('Header not found, returning empty array');
            return [];
        }

        // Extract dates from header
        const headerLine = lines[headerIndex];
        console.log('Header line for dates:', headerLine);
        const dates = this.extractDatesFromHeader(headerLine);
        console.log('Extracted dates:', dates);

        // Initialize portfolio entries for each date
        const portfolioMap: { [date: string]: PortfolioEntry } = {};
        dates.forEach(date => {
            portfolioMap[date] = {
                date,
                trow: 0,
                robinhood: 0,
                etrade: 0,
                teradata: 0
            };
        });
        console.log('Initialized portfolioMap:', portfolioMap);

        // Parse each account row
        for (let i = headerIndex + 1; i < lines.length; i++) {
            const line = lines[i];
            console.log(`Scanning row ${i}:`, line);
            const lowerLine = line.toLowerCase();

            let accountType: keyof PortfolioEntry | null = null;
            if (lowerLine.includes('trow') || lowerLine.includes('t.row')) {
                accountType = 'trow';
            } else if (lowerLine.includes('robin') && lowerLine.includes('hood')) {
                accountType = 'robinhood';
            } else if (lowerLine.includes('e*trade') || lowerLine.includes('etrade')) {
                accountType = 'etrade';
            } else if (lowerLine.includes('teradata')) {
                accountType = 'teradata';
            } else if (lowerLine.includes('total')) {
                console.log('Skipping total line');
                continue;
            }

            if (accountType) {
                console.log(`Account type detected: ${accountType}`);
                const values = this.extractValuesFromRow(line);
                console.log('Extracted values:', values);
                // Map values to dates
                for (let j = 0; j < Math.min(dates.length, values.length); j++) {
                    const date = dates[j];
                    const oldValue = portfolioMap[date][accountType];
                    portfolioMap[date][accountType] = values[j];
                    console.log(`Mapping ${accountType} for ${date}: ${oldValue} -> ${values[j]}`);
                }
            }
        }

        // Convert map to array and sort by date
        const portfolioEntries = Object.values(portfolioMap);
        portfolioEntries.sort((a, b) => a.date.localeCompare(b.date));
        console.log('Final sorted portfolio entries:', portfolioEntries);

        return portfolioEntries;
    }

    private static extractDatesFromHeader(headerLine: string): string[] {
        console.log('extractDatesFromHeader: start');
        console.log('Header line:', headerLine);

        const dates: string[] = [];
        const columns = headerLine.split(/\t+|\s{2,}/);
        console.log('Columns:', columns);

        const monthMap: { [key: string]: string } = {
            jan: '01',
            feb: '02',
            mar: '03',
            apr: '04',
            may: '05',
            jun: '06',
            jul: '07',
            aug: '08',
            sep: '09',
            oct: '10',
            nov: '11',
            dec: '12'
        };

        for (let i = 0; i < columns.length; i++) {
            const column = columns[i];
            const dateMatch = column.match(/(\w+)\s*\d*,?\s*(\d{4})/i);
            if (dateMatch) {
                const monthStr = dateMatch[1].toLowerCase();
                const year = dateMatch[2];
                const key = monthStr.substring(0, 3);
                const month = monthMap[key];
                if (month) {
                    const formatted = `${year}-${month}`;
                    dates.push(formatted);
                    console.log(`Matched date column "${column}" -> ${formatted}`);
                }
            } else {
                // Check if this column is a month name and the next column is a year
                const monthMatch = column.match(/^(\w+)$/i);
                if (monthMatch && i + 1 < columns.length) {
                    const nextColumn = columns[i + 1];
                    const yearMatch = nextColumn.match(/^(\d{4})$/);
                    if (yearMatch) {
                        const monthStr = monthMatch[1].toLowerCase();
                        const year = yearMatch[1];
                        const key = monthStr.substring(0, 3);
                        const month = monthMap[key];
                        if (month) {
                            const formatted = `${year}-${month}`;
                            dates.push(formatted);
                            console.log(`Matched split date columns "${column}" + "${nextColumn}" -> ${formatted}`);
                            i++; // Skip the next column since we've processed it
                        }
                    }
                }
            }
        }

        console.log('extractDatesFromHeader: result', dates);
        return dates;
    }

    private static extractValuesFromRow(row: string): number[] {
        console.log('extractValuesFromRow: start for row:', row);
        const values: number[] = [];

        const parts = row.split(/\t+|\s{2,}/);
        console.log('Row parts:', parts);

        for (let i = 1; i < parts.length; i++) {
            let part = parts[i].trim();

            if (part === '$70,0000') {
                console.log('Applying typo fix for part:', part);
                part = '70000';
            }

            const num = parseFloat(part.replace(/\$/g, '').replace(/,/g, ''));
            if (!isNaN(num)) {
                values.push(num);
                console.log(`Parsed numeric value from "${part}" ->`, num);
            } else {
                console.log(`Skipping non-numeric part: "${part}"`);
            }
        }

        console.log('extractValuesFromRow: result', values);
        return values;
    }

    static validatePortfolioData(entry: PortfolioEntry): boolean {
        console.log('validatePortfolioData: entry', entry);
        if (!entry.date || typeof entry.date !== 'string') {
            console.log('Invalid date format');
            return false;
        }

        if (!entry.date.match(/^\d{4}-\d{2}$/)) {
            console.log('Date does not match YYYY-MM');
            return false;
        }

        const requiredFields: (keyof PortfolioEntry)[] = ['trow', 'robinhood', 'etrade', 'teradata'];
        for (const field of requiredFields) {
            const value = entry[field];
            console.log(`Validating ${field}:`, value);
            if (typeof value !== 'number' || value < 0) {
                console.log(`${field} is invalid`);
                return false;
            }
        }

        console.log('validatePortfolioData: valid');
        return true;
    }

    static formatPortfolioForOutput(entries: PortfolioEntry[]): string {
        console.log('formatPortfolioForOutput: start', entries);
        let output = 'export const GENERATED_PORTFOLIO_DATA: PortfolioEntry[] = [\n';

        entries.forEach((entry, index) => {
            console.log(`Formatting entry ${index}:`, entry);
            output += `  {\n`;
            output += `    date: "${entry.date}",\n`;
            output += `    trow: ${entry.trow},\n`;
            output += `    robinhood: ${entry.robinhood},\n`;
            output += `    etrade: ${entry.etrade},\n`;
            output += `    teradata: ${entry.teradata}`;
            if (entry.fidelity) {
                output += `,\n    fidelity: ${entry.fidelity}`;
            }
            output += `\n  }`;
            output += index < entries.length - 1 ? ',\n' : '\n';
        });

        output += '];';
        console.log('formatPortfolioForOutput: result', output);
        return output;
    }
}
