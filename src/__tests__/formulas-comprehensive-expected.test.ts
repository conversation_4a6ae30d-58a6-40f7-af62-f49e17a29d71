import * as formulas from '../formulas';
import { expectedTestData } from './data/expected';

describe('Financial Formulas - Comprehensive Tests with Expected Data', () => {
  const { meta, test_cases, formula_tests } = expectedTestData;

  describe('D-3: Months to Target - All Expected Values', () => {
    test_cases.forEach((testCase, index) => {
      it(`should calculate ${testCase.expect_months} months for PMT=${testCase.PMT} to reach $${testCase.FV.toLocaleString()}`, () => {
        const months = formulas.monthsToTarget(
          meta.start_balance,
          testCase.FV,
          meta.monthly_growth_rate,
          testCase.PMT
        );
        
        expect(months).toBe(testCase.expect_months);
      });
    });
  });

  describe('D-4: Delta Months Between Contributions', () => {
    it('should calculate delay between $6250 and $5260 for $1M target', () => {
      const delta = formulas.deltaMonthsBetweenContributions(
        meta.start_balance,
        1000000,
        meta.monthly_growth_rate,
        6250,
        5260
      );
      expect(delta).toBe(1);
    });

    it('should calculate delay between $6250 and $4200 for $1M target', () => {
      const delta = formulas.deltaMonthsBetweenContributions(
        meta.start_balance,
        1000000,
        meta.monthly_growth_rate,
        6250,
        4200
      );
      expect(delta).toBe(1);
    });

    it('should calculate delay between $6250 and $5260 for $1.5M target', () => {
      const delta = formulas.deltaMonthsBetweenContributions(
        meta.start_balance,
        1500000,
        meta.monthly_growth_rate,
        6250,
        5260
      );
      expect(delta).toBe(0);
    });

    it('should calculate delay between $6250 and $4200 for $1.5M target', () => {
      const delta = formulas.deltaMonthsBetweenContributions(
        meta.start_balance,
        1500000,
        meta.monthly_growth_rate,
        6250,
        4200
      );
      expect(delta).toBe(1);
    });

    it('should calculate delay between $6250 and $5260 for $2M target', () => {
      const delta = formulas.deltaMonthsBetweenContributions(
        meta.start_balance,
        2000000,
        meta.monthly_growth_rate,
        6250,
        5260
      );
      expect(delta).toBe(1);
    });

    it('should calculate delay between $6250 and $4200 for $2M target', () => {
      const delta = formulas.deltaMonthsBetweenContributions(
        meta.start_balance,
        2000000,
        meta.monthly_growth_rate,
        6250,
        4200
      );
      expect(delta).toBe(2);
    });
  });

  describe('D-1: Future Value with Contributions', () => {
    const futureValueTests = formula_tests.filter(test => test.formula === 'D-1');

    futureValueTests.forEach(test => {
      it(`should calculate future value for ${test.name}`, () => {
        // Type guards to ensure all required values are defined
        if (test.inputs.PV === undefined || test.inputs.r === undefined ||
            test.inputs.PMT === undefined || test.inputs.n === undefined ||
            test.expect_FV === undefined) {
          throw new Error(`Missing required test data for ${test.name}`);
        }

        const fv = formulas.futureValueWithContributions(
          test.inputs.PV,
          test.inputs.r,
          test.inputs.PMT,
          test.inputs.n
        );

        expect(Math.round(fv)).toBeCloseTo(test.expect_FV, -2); // Within $100
      });
    });
  });

  describe('F-6: Required Contribution for Target', () => {
    const requiredPMTTests = formula_tests.filter(test => test.formula === 'F-6');

    requiredPMTTests.forEach(test => {
      it(`should calculate required contribution for ${test.name}`, () => {
        // Type guards to ensure all required values are defined
        if (test.inputs.PV === undefined || test.inputs.FV === undefined ||
            test.inputs.r === undefined || test.inputs.n === undefined ||
            test.expect_PMT === undefined) {
          throw new Error(`Missing required test data for ${test.name}`);
        }

        const pmt = formulas.requiredContributionForTarget(
          test.inputs.PV,
          test.inputs.FV,
          test.inputs.r,
          test.inputs.n
        );

        expect(pmt).toBeCloseTo(test.expect_PMT, 2);
      });
    });
  });

  describe('F-8: Component Split', () => {
    const componentTests = formula_tests.filter(test => test.formula === 'F-8');

    componentTests.forEach(test => {
      it(`should calculate component split for ${test.name}`, () => {
        // Type guards to ensure all required values are defined
        if (test.inputs.PV === undefined || test.inputs.r === undefined ||
            test.inputs.PMT === undefined || test.inputs.n === undefined ||
            !test.expect) {
          throw new Error(`Missing required test data for ${test.name}`);
        }

        const result = formulas.componentSplit(
          test.inputs.PV,
          test.inputs.r,
          test.inputs.PMT,
          test.inputs.n
        );

        expect(result.FV_total).toBeCloseTo(test.expect.FV_total, -2);
        expect(result.FV_from_PV).toBeCloseTo(test.expect.FV_from_PV, -2);
        expect(result.FV_from_contr).toBeCloseTo(test.expect.FV_from_contr, -2);
        expect(result.share_contr).toBeCloseTo(test.expect.share_contr, 3);
      });
    });
  });

  describe('F-9: Coast-FIRE Threshold', () => {
    const coastFireTests = formula_tests.filter(test => test.formula === 'F-9');

    coastFireTests.forEach(test => {
      it(`should calculate coast-FIRE threshold for ${test.name}`, () => {
        // Type guards to ensure all required values are defined
        if (test.inputs.monthly_r === undefined || test.inputs.PMT === undefined ||
            test.expect_PV_coast === undefined) {
          throw new Error(`Missing required test data for ${test.name}`);
        }

        const threshold = formulas.coastFireThreshold(
          test.inputs.monthly_r,
          test.inputs.PMT
        );

        expect(threshold).toBeCloseTo(test.expect_PV_coast, -2);
      });
    });
  });

  describe('Date Calculations', () => {
    function addMonthsToDate(startDate: string, months: number): string {
      // Parse the date components manually to avoid timezone issues
      const [year, month, day] = startDate.split('-').map(Number);

      // Calculate new year and month
      let newYear = year;
      let newMonth = month + months;

      // Handle year overflow
      while (newMonth > 12) {
        newYear++;
        newMonth -= 12;
      }

      // Handle year underflow (though not needed for this test)
      while (newMonth < 1) {
        newYear--;
        newMonth += 12;
      }

      // Format the result
      const monthStr = newMonth.toString().padStart(2, '0');
      const dayStr = day.toString().padStart(2, '0');

      return `${newYear}-${monthStr}-${dayStr}`;
    }

    test_cases.forEach((testCase, index) => {
      it(`should reach $${testCase.FV.toLocaleString()} by ${testCase.expect_date} with PMT=${testCase.PMT}`, () => {
        const calculatedDate = addMonthsToDate(meta.start_date_ISO, testCase.expect_months);
        expect(calculatedDate).toBe(testCase.expect_date);
      });
    });
  });

  describe('Cross-validation Tests', () => {
    it('should validate that D-1 and D-3 formulas are inverses', () => {
      // Test: If we calculate months to reach a target, then use those months in FV formula, we should get the target
      const pv = meta.start_balance;
      const target = 1000000;
      const r = meta.monthly_growth_rate;
      const pmt = 6250;
      
      const months = formulas.monthsToTarget(pv, target, r, pmt);
      const calculatedFV = formulas.futureValueWithContributions(pv, r, pmt, months);
      
      expect(calculatedFV).toBeGreaterThanOrEqual(target);
      expect(calculatedFV).toBeLessThan(target * 1.05); // Within 5% (due to rounding up months)
    });

    it('should validate that F-6 produces correct PMT for exact target', () => {
      // Test: Calculate required PMT, then use it in FV formula to verify we hit the target
      const pv = meta.start_balance;
      const target = 2000000;
      const r = meta.monthly_growth_rate;
      const months = 36;
      
      const requiredPMT = formulas.requiredContributionForTarget(pv, target, r, months);
      const achievedFV = formulas.futureValueWithContributions(pv, r, requiredPMT, months);
      
      expect(achievedFV).toBeCloseTo(target, -1); // Within $10
    });
  });
});
