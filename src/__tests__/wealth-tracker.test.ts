import { WealthTracker } from '../wealth-tracker';
import { PortfolioEntry, PayslipEntry } from '../types';
import * as fs from 'fs';
import { INITIAL_PORTFOLIO_DATA } from '../data/portfolio-data';
import { INITIAL_PAYSLIP_DATA } from '../data/payslip-data';

jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('WealthTracker', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    tracker = new WealthTracker();
    // Initialize with real data
    (tracker as any).portfolioHistory = [...INITIAL_PORTFOLIO_DATA];
    (tracker as any).payslipHistory = [...INITIAL_PAYSLIP_DATA];
    jest.clearAllMocks();
  });

  describe('addPortfolioData', () => {
    it('should add new portfolio data', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.addPortfolioData(2025, 8, 280000, 120000, 370000, 36000, 5000);
      
      expect(consoleSpy).toHaveBeenCalledWith('\n✅ Added portfolio data for August 2025');
      expect(consoleSpy).toHaveBeenCalledWith('   Total Portfolio Value: $811,000');
      
      consoleSpy.mockRestore();
    });

    it('should handle portfolio data without fidelity', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.addPortfolioData(2025, 8, 280000, 120000, 370000, 36000);
      
      expect(consoleSpy).toHaveBeenCalledWith('   Total Portfolio Value: $806,000');
      
      consoleSpy.mockRestore();
    });
  });

  describe('addPayslip', () => {
    it('should add new payslip data', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.addPayslip('2025-07-15', 6419.23, 1287.33, 962.89, 1669.00, 256.77);
      
      expect(consoleSpy).toHaveBeenCalledWith('\n✅ Added payslip for 2025-07-15');
      expect(consoleSpy).toHaveBeenCalledWith('   Net Pay: $1,287.33');
      expect(consoleSpy).toHaveBeenCalledWith('   Total Investments: $2,888.66');
      
      consoleSpy.mockRestore();
    });

    it('should sort payslips chronologically', () => {
      tracker.addPayslip('2025-07-15', 6419.23, 1287.33, 962.89, 1669.00, 256.77);
      tracker.addPayslip('2025-07-01', 6419.23, 1287.33, 962.89, 1669.00, 256.77);
      
      // Access private property for testing
      const payslips = (tracker as any).payslipHistory;
      const recentPayslips = payslips.slice(-2);
      
      expect(recentPayslips[0].date).toBe('2025-07-01');
      expect(recentPayslips[1].date).toBe('2025-07-15');
    });
  });

  describe('calculateAverageMonthlyContribution', () => {
    it('should calculate average monthly contribution', () => {
      // Add some test payslips
      tracker.addPayslip('2025-07-15', 6419.23, 1287.33, 962.89, 1669.00, 256.77);
      tracker.addPayslip('2025-07-01', 6419.23, 1287.33, 962.89, 1669.00, 256.77);
      
      const avgContribution = tracker.calculateAverageMonthlyContribution();
      
      expect(avgContribution).toBeGreaterThan(0);
      expect(typeof avgContribution).toBe('number');
    });

    it('should return default value for empty payslips', () => {
      // Create a tracker with no payslips
      const emptyTracker = new WealthTracker();
      // Clear the initial data
      (emptyTracker as any).payslipHistory = [];
      
      const avgContribution = emptyTracker.calculateAverageMonthlyContribution();
      
      expect(avgContribution).toBe(6248);
    });
  });

  describe('calculateIrr', () => {
    it('should calculate IRR', () => {
      const irr = tracker.calculateIrr();

      expect(typeof irr).toBe('number');
      expect(isFinite(irr)).toBe(true);
      // IRR can be negative for poor performing investments, so just check it's a reasonable number
      expect(irr).toBeGreaterThan(-10);
      expect(irr).toBeLessThan(10);
    });
  });

  describe('calculatePeriodReturns', () => {
    it('should calculate period returns', () => {
      const returns = tracker.calculatePeriodReturns();
      
      expect(Array.isArray(returns)).toBe(true);
      expect(returns.length).toBeGreaterThan(0);
      
      returns.forEach(returnValue => {
        expect(typeof returnValue).toBe('number');
      });
    });
  });

  describe('calculateStatistics', () => {
    it('should calculate mean return and standard deviation', () => {
      const stats = tracker.calculateStatistics();
      
      expect(stats).toHaveProperty('meanReturn');
      expect(stats).toHaveProperty('stdDev');
      expect(typeof stats.meanReturn).toBe('number');
      expect(typeof stats.stdDev).toBe('number');
      expect(stats.stdDev).toBeGreaterThanOrEqual(0);
    });
  });

  describe('projectFuture', () => {
    it('should project months to reach target', () => {
      const months = tracker.projectFuture(1000000, 0.02, 5000);
      
      expect(typeof months).toBe('number');
      expect(months).toBeGreaterThanOrEqual(0);
    });

    it('should handle zero monthly rate', () => {
      const months = tracker.projectFuture(1000000, 0, 5000);
      
      expect(typeof months).toBe('number');
      expect(months).toBeGreaterThanOrEqual(0);
    });

    it('should handle negative monthly rate', () => {
      const months = tracker.projectFuture(1000000, -0.01, 5000);
      
      expect(typeof months).toBe('number');
      expect(months).toBeGreaterThanOrEqual(0);
    });
  });

  describe('analyzePayslips', () => {
    it('should analyze payslips without errors', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.analyzePayslips();
      
      expect(consoleSpy).toHaveBeenCalledWith('\n💰 PAYSLIP ANALYSIS');
      
      consoleSpy.mockRestore();
    });
  });

  describe('analyzeAndProject', () => {
    it('should run complete analysis without errors', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.analyzeAndProject();
      
      expect(consoleSpy).toHaveBeenCalledWith('\n' + '='.repeat(60));
      expect(consoleSpy).toHaveBeenCalledWith('WEALTH TRACKER ANALYSIS');
      
      consoleSpy.mockRestore();
    });
  });

  describe('showPortfolioHistory', () => {
    it('should display portfolio history', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.showPortfolioHistory();
      
      expect(consoleSpy).toHaveBeenCalledWith('\n📋 PORTFOLIO HISTORY');
      
      consoleSpy.mockRestore();
    });
  });

  describe('showRecentPayslips', () => {
    it('should display recent payslips', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.showRecentPayslips();
      
      expect(consoleSpy).toHaveBeenCalledWith('\n📋 RECENT PAYSLIPS (Last 10)');
      
      consoleSpy.mockRestore();
    });
  });

  describe('saveData', () => {
    it('should save data to file', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      mockFs.writeFileSync.mockImplementation();
      
      tracker.saveData('test.json');
      
      expect(mockFs.writeFileSync).toHaveBeenCalledWith('test.json', expect.any(String));
      expect(consoleSpy).toHaveBeenCalledWith('\n💾 Data saved to test.json');
      
      consoleSpy.mockRestore();
    });

    it('should use default filename when none provided', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      mockFs.writeFileSync.mockImplementation();
      
      tracker.saveData();
      
      expect(mockFs.writeFileSync).toHaveBeenCalledWith('wealth_tracker_data.json', expect.any(String));
      
      consoleSpy.mockRestore();
    });
  });

  describe('loadData', () => {
    it('should load data from file', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const mockData = {
        portfolio_history: [],
        payslip_history: []
      };
      
      mockFs.readFileSync.mockReturnValue(JSON.stringify(mockData));
      
      tracker.loadData('test.json');
      
      expect(mockFs.readFileSync).toHaveBeenCalledWith('test.json', 'utf8');
      expect(consoleSpy).toHaveBeenCalledWith('\n📂 Data loaded from test.json');
      
      consoleSpy.mockRestore();
    });

    it('should handle missing file gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('File not found');
      });
      
      tracker.loadData('nonexistent.json');
      
      expect(consoleSpy).toHaveBeenCalledWith('\n❌ No saved data found at nonexistent.json');
      
      consoleSpy.mockRestore();
    });
  });

  describe('private methods', () => {
    it('should calculate total value correctly', () => {
      const entry: PortfolioEntry = {
        date: '2025-08',
        trow: 100000,
        robinhood: 50000,
        etrade: 75000,
        teradata: 25000,
        fidelity: 10000
      };
      
      const totalValue = (tracker as any).getTotalValue(entry);
      expect(totalValue).toBe(260000);
    });

    it('should calculate total value without fidelity', () => {
      const entry: PortfolioEntry = {
        date: '2025-08',
        trow: 100000,
        robinhood: 50000,
        etrade: 75000,
        teradata: 25000
      };
      
      const totalValue = (tracker as any).getTotalValue(entry);
      expect(totalValue).toBe(250000);
    });

    it('should format date correctly', () => {
      const formattedDate = (tracker as any).formatDate('2025-08');
      expect(formattedDate).toBe('August 2025');
    });

    it('should calculate months elapsed', () => {
      const monthsElapsed = (tracker as any).getMonthsElapsed();
      expect(Array.isArray(monthsElapsed)).toBe(true);
      expect(monthsElapsed.length).toBeGreaterThan(0);
      expect(monthsElapsed[0]).toBe(0);
    });

    it('should detect bonus month crossing', () => {
      const crossesBonus = (tracker as any).crossesBonusMonth(2); // Index 2 should cross March
      expect(typeof crossesBonus).toBe('boolean');
    });

    it('should calculate months after bonus', () => {
      const monthsAfter = (tracker as any).monthsAfterBonus(5);
      expect(typeof monthsAfter).toBe('number');
      expect(monthsAfter).toBeGreaterThanOrEqual(0);
    });
  });
});