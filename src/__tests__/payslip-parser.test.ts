import {PayslipParser} from '../payslip-parser';
import {PayslipEntry} from '../types';

describe('PayslipParser', () => {
    const samplePayslipText = `Company Information

Name
Filter column

Address
Filter column

Phone
Filter column
GPS Services, Inc.
2 Folsom Street
San Francisco, CA 94105
United States of America
******-411-2772
Payslip Information1 item



Payslip Information

Name
Filter column

Employee ID
Filter column

Pay Period Begin
Filter column

Pay Period End
Filter column

Check Date
Filter column

Check Number
Filter column
Karanvir Sagoo
3080779
03/09/2025
03/22/2025
03/26/2025
Current and YTD Totals2 items



Current and YTD Totals

Balance Period
Sort and filter column

Hours Worked
Sort and filter column

Gross Pay
Sort and filter column

Pre Tax Deductions
Sort and filter column

EE Taxes
Sort and filter column

Post Tax Deductions
Sort and filter column

Net Pay
Sort and filter column
Current
56.00
6,419.23
103.79
2,139.45
2,888.66
1,287.33
YTD
504.00
44,934.61
492.70
15,076.91
18,294.84
11,070.16
Post Tax Deductions4 items



Post Tax Deductions

Description
Sort and filter column

Amount
Sort and filter column

YTD
Sort and filter column
ESPP
962.89
6,740.22
GapShare Roth E
1,669.00
10,014.00
GapShare Roth R
256.77
1,540.62
Total:
2,888.66
18,294.84`;

    const expectedPayslip: PayslipEntry = {
        date: '2025-03-09',
        gross: 6419.23,
        net: 1287.33,
        espp: 962.89,
        roth_e: 1669.00,
        roth_r: 256.77,
        total_invest: 2888.66
    };

    describe('parsePayslip', () => {
        it('should parse a complete payslip correctly', () => {
            const result = PayslipParser.parsePayslip(samplePayslipText);
            expect(result).toEqual(expectedPayslip);
        });

        it('should throw error for invalid payslip data', () => {
            expect(() => PayslipParser.parsePayslip('Invalid payslip data')).toThrow();
        });

        it('should handle missing optional fields', () => {
            const payslipWithoutESPP = `
Check Date
03/26/2025

Current and YTD Totals

Gross Pay
6,419.23

Net Pay
1,287.33

Post Tax Deductions

GapShare Roth E
1,669.00

GapShare Roth R
256.77
      `;
            const result = PayslipParser.parsePayslip(payslipWithoutESPP);
            expect(result.espp).toBe(0);
            expect(result.roth_e).toBe(1669.00);
            expect(result.roth_r).toBe(256.77);
        });
    });

    describe('parseMultiplePayslips', () => {
        it('should parse multiple payslips and sort by date', () => {
            const p1 = samplePayslipText.replace('03/26/2025', '03/12/2025');
            const p2 = samplePayslipText;
            const result = PayslipParser.parseMultiplePayslips([p2, p1]);
            expect(result).toHaveLength(2);
            expect(result[0].date).toBe('2025-03-12');
            expect(result[1].date).toBe('2025-03-26');
        });

        it('should handle empty array', () => {
            expect(PayslipParser.parseMultiplePayslips([])).toHaveLength(0);
        });

        it('should skip invalid payslips', () => {
            const result = PayslipParser.parseMultiplePayslips([samplePayslipText, 'invalid']);
            expect(result).toHaveLength(1);
        });
    });

    describe('validatePayslipData', () => {
        it('should validate correct data', () => {
            expect(PayslipParser.validatePayslipData(expectedPayslip)).toBe(true);
        });
        it('should reject missing fields', () => {
            expect(
                PayslipParser.validatePayslipData({...expectedPayslip, date: ''} as any)
            ).toBe(false);
        });
        it('should reject negative values', () => {
            expect(
                PayslipParser.validatePayslipData({...expectedPayslip, gross: -1} as any)
            ).toBe(false);
        });
        it('should reject mismatched total_invest', () => {
            expect(
                PayslipParser.validatePayslipData({...expectedPayslip, total_invest: 0} as any)
            ).toBe(false);
        });
    });

    describe('formatPayslipData', () => {
        it('should format for output', () => {
            const arr: PayslipEntry[] = [
                {...expectedPayslip, date: '2025-03-12', net: 1287.32, espp: 962.88, total_invest: 2888.65},
                expectedPayslip
            ];
            const out = PayslipParser.formatPayslipData(arr);
            expect(out).toContain('export const GENERATED_PAYSLIP_DATA');
            expect(out).toContain('date: "2025-03-12"');
            expect(out).toContain('espp: 962.88');
            expect(out).toContain('total_invest: 2888.65');
        });
    });
});
