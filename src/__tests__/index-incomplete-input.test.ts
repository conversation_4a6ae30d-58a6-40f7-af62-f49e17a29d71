import * as readline from 'node:readline';
import { WealthTracker } from '../wealth-tracker';
import { PayslipParser } from '../payslip-parser';

// Mock the modules
jest.mock('../wealth-tracker');
jest.mock('../payslip-parser');
jest.mock('node:readline');

const MockWealthTracker = WealthTracker as jest.MockedClass<typeof WealthTracker>;
const MockPayslipParser = PayslipParser as jest.MockedClass<typeof PayslipParser>;
const mockReadline = readline as jest.Mocked<typeof readline>;

describe('Index.ts - Incomplete Input Handling Integration', () => {
  let mockTracker: jest.Mocked<WealthTracker>;
  let mockCreateInterface: jest.Mock;
  let mockClose: jest.Mock;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock WealthTracker instance
    mockTracker = {
      loadData: jest.fn(),
      addPayslip: jest.fn(),
      addPortfolioDataIfComplete: jest.fn(),
      getMostRecentPortfolioEntry: jest.fn(),
      getPayslipHistory: jest.fn(),
      hasSufficientDataForAnalysis: jest.fn(),
      analyzeAndProject: jest.fn(),
      saveData: jest.fn(),
    } as any;

    MockWealthTracker.mockImplementation(() => mockTracker);

    // Mock PayslipParser
    MockPayslipParser.parseMultiplePayslips = jest.fn();
    MockPayslipParser.validatePayslipData = jest.fn();

    // Mock readline
    mockClose = jest.fn();
    mockCreateInterface = jest.fn(() => ({
      question: jest.fn(),
      close: mockClose,
      on: jest.fn(),
      off: jest.fn(),
    }));

    mockReadline.createInterface = mockCreateInterface;

    // Mock console.log to reduce noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('WealthTracker Integration with Incomplete Input', () => {
    it('should handle incomplete portfolio data gracefully', () => {
      // Test that addPortfolioDataIfComplete works as expected
      mockTracker.addPortfolioDataIfComplete.mockReturnValue(false);
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue({
        date: '2025-07',
        trow: 270000,
        robinhood: 115000,
        etrade: 366000,
        teradata: 35000
      });

      // Simulate incomplete input
      const result = mockTracker.addPortfolioDataIfComplete(undefined, 8, 280000, 120000, 370000, 36000);
      expect(result).toBe(false);

      // Should still be able to get recent portfolio data
      const recentData = mockTracker.getMostRecentPortfolioEntry();
      expect(recentData).not.toBeNull();
      expect(recentData?.date).toBe('2025-07');
    });

    it('should handle missing payslip data gracefully', () => {
      // Test that we can still analyze with existing payslip data
      mockTracker.getPayslipHistory.mockReturnValue([
        { date: '2025-07-01', gross: 6000, net: 4000, espp: 500, roth_e: 1000, roth_r: 200, total_invest: 1700 },
        { date: '2025-06-15', gross: 6200, net: 4100, espp: 520, roth_e: 1050, roth_r: 210, total_invest: 1780 }
      ]);
      mockTracker.hasSufficientDataForAnalysis.mockReturnValue(true);

      const payslips = mockTracker.getPayslipHistory();
      expect(payslips).toHaveLength(2);
      expect(mockTracker.hasSufficientDataForAnalysis()).toBe(true);
    });

    it('should validate sufficient data for analysis', () => {
      // Test with sufficient data
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue({
        date: '2025-07',
        trow: 270000,
        robinhood: 115000,
        etrade: 366000,
        teradata: 35000
      });
      mockTracker.getPayslipHistory.mockReturnValue([
        { date: '2025-07-01', gross: 6000, net: 4000, espp: 500, roth_e: 1000, roth_r: 200, total_invest: 1700 }
      ]);
      mockTracker.hasSufficientDataForAnalysis.mockReturnValue(true);

      expect(mockTracker.getMostRecentPortfolioEntry()).not.toBeNull();
      expect(mockTracker.getPayslipHistory()).toHaveLength(1);
      expect(mockTracker.hasSufficientDataForAnalysis()).toBe(true);
    });
  });

  describe('Payslip Input Handling', () => {
    it('should handle no payslip input gracefully', async () => {
      mockQuestion
        .mockResolvedValueOnce('') // year (empty)
        .mockResolvedValueOnce('') // month (empty);
      
      mockMultilineQuestion.mockResolvedValue(''); // Empty payslip
      
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue({
        date: '2025-07',
        trow: 270000,
        robinhood: 115000,
        etrade: 366000,
        teradata: 35000
      });
      mockTracker.getPayslipHistory.mockReturnValue([
        { date: '2025-07-01', gross: 6000, net: 4000, espp: 500, roth_e: 1000, roth_r: 200, total_invest: 1700 }
      ]);
      mockTracker.hasSufficientDataForAnalysis.mockReturnValue(true);
      
      const { default: main } = await import('../index');
      jest.doMock('node:readline', () => mockReadline);
      
      await expect(main()).resolves.not.toThrow();
      
      expect(console.log).toHaveBeenCalledWith('📋 No new payslips provided - using existing payslip data for analysis');
      expect(MockPayslipParser.parseMultiplePayslips).not.toHaveBeenCalled();
    });

    it('should process partial payslip input', async () => {
      const samplePayslip = `
Check Date
03/09/2025
Gross Pay
6,419.23
Net Pay
1,287.33
ESPP
962.89
GapShare Roth E
1,669.00
GapShare Roth R
256.77`;

      mockQuestion
        .mockResolvedValueOnce('') // year (empty)
        .mockResolvedValueOnce(''); // month (empty)
      
      mockMultilineQuestion
        .mockResolvedValueOnce(samplePayslip) // payslip 1
        .mockResolvedValueOnce(''); // payslip 2 (empty, should stop asking)
      
      const mockParsedPayslip = {
        date: '2025-03-09',
        gross: 6419.23,
        net: 1287.33,
        espp: 962.89,
        roth_e: 1669.00,
        roth_r: 256.77,
        total_invest: 2888.66
      };
      
      MockPayslipParser.parseMultiplePayslips.mockReturnValue([mockParsedPayslip]);
      MockPayslipParser.validatePayslipData.mockReturnValue(true);
      
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue({
        date: '2025-07',
        trow: 270000,
        robinhood: 115000,
        etrade: 366000,
        teradata: 35000
      });
      mockTracker.getPayslipHistory.mockReturnValue([mockParsedPayslip]);
      mockTracker.hasSufficientDataForAnalysis.mockReturnValue(true);
      
      const { default: main } = await import('../index');
      jest.doMock('node:readline', () => mockReadline);
      
      await expect(main()).resolves.not.toThrow();
      
      expect(MockPayslipParser.parseMultiplePayslips).toHaveBeenCalledWith([samplePayslip]);
      expect(mockTracker.addPayslip).toHaveBeenCalledWith(
        '2025-03-09', 6419.23, 1287.33, 962.89, 1669.00, 256.77
      );
      expect(console.log).toHaveBeenCalledWith('✅ Successfully parsed 1 new payslips');
    });
  });

  describe('Portfolio Input Handling', () => {
    it('should skip portfolio values when fields are empty', async () => {
      mockQuestion
        .mockResolvedValueOnce('2025') // year
        .mockResolvedValueOnce('8') // month
        .mockResolvedValueOnce('') // payslip 1
        .mockResolvedValueOnce('') // trow (empty)
        .mockResolvedValueOnce('120000') // robinhood
        .mockResolvedValueOnce('370000') // etrade
        .mockResolvedValueOnce('36000') // teradata
        .mockResolvedValueOnce('5000'); // fidelity
      
      mockMultilineQuestion.mockResolvedValue('');
      
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue({
        date: '2025-07',
        trow: 270000,
        robinhood: 115000,
        etrade: 366000,
        teradata: 35000
      });
      mockTracker.getPayslipHistory.mockReturnValue([
        { date: '2025-07-01', gross: 6000, net: 4000, espp: 500, roth_e: 1000, roth_r: 200, total_invest: 1700 }
      ]);
      mockTracker.hasSufficientDataForAnalysis.mockReturnValue(true);
      mockTracker.addPortfolioDataIfComplete.mockReturnValue(false); // Incomplete data
      
      const { default: main } = await import('../index');
      jest.doMock('node:readline', () => mockReadline);
      
      await expect(main()).resolves.not.toThrow();
      
      expect(mockTracker.addPortfolioDataIfComplete).toHaveBeenCalledWith(2025, 8, undefined, 120000, 370000, 36000, 5000);
      expect(console.log).toHaveBeenCalledWith('📊 Using most recent portfolio data from 2025-07 for analysis');
    });
  });

  describe('Error Handling', () => {
    it('should exit early when no portfolio data is available', async () => {
      mockQuestion
        .mockResolvedValueOnce('') // year (empty)
        .mockResolvedValueOnce(''); // month (empty)
      
      mockMultilineQuestion.mockResolvedValue('');
      
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue(null); // No portfolio data
      
      const { default: main } = await import('../index');
      jest.doMock('node:readline', () => mockReadline);
      
      await expect(main()).resolves.not.toThrow();
      
      expect(console.log).toHaveBeenCalledWith('❌ Error: No portfolio data available for analysis');
      expect(mockTracker.analyzeAndProject).not.toHaveBeenCalled();
    });

    it('should exit early when insufficient data for analysis', async () => {
      mockQuestion
        .mockResolvedValueOnce('') // year (empty)
        .mockResolvedValueOnce(''); // month (empty)
      
      mockMultilineQuestion.mockResolvedValue('');
      
      mockTracker.getMostRecentPortfolioEntry.mockReturnValue({
        date: '2025-07',
        trow: 270000,
        robinhood: 115000,
        etrade: 366000,
        teradata: 35000
      });
      mockTracker.hasSufficientDataForAnalysis.mockReturnValue(false); // Insufficient data
      
      const { default: main } = await import('../index');
      jest.doMock('node:readline', () => mockReadline);
      
      await expect(main()).resolves.not.toThrow();
      
      expect(console.log).toHaveBeenCalledWith('❌ Error: Insufficient data for analysis. Need at least one portfolio entry and one payslip entry.');
      expect(mockTracker.analyzeAndProject).not.toHaveBeenCalled();
    });
  });
});
