import { WealthTracker } from '../wealth-tracker';
import { PortfolioEntry, PayslipEntry } from '../types';

describe('WealthTracker - Incomplete Input Handling', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    tracker = new WealthTracker();
  });

  describe('getMostRecentPortfolioEntry', () => {
    it('should return null when no portfolio data exists', () => {
      // Create a tracker with no initial data
      const emptyTracker = new (class extends WealthTracker {
        constructor() {
          super();
          // Clear the initial data for this test
          (this as any).portfolioHistory = [];
        }
      })();
      
      const result = emptyTracker.getMostRecentPortfolioEntry();
      expect(result).toBeNull();
    });

    it('should return the most recent portfolio entry', () => {
      const result = tracker.getMostRecentPortfolioEntry();
      expect(result).not.toBeNull();
      expect(result?.date).toBe('2025-07'); // Based on INITIAL_PORTFOLIO_DATA
      expect(result?.trow).toBe(270000);
      expect(result?.robinhood).toBe(115000);
      expect(result?.etrade).toBe(366000);
      expect(result?.teradata).toBe(35000);
    });

    it('should return a copy, not the original object', () => {
      const result = tracker.getMostRecentPortfolioEntry();
      const originalHistory = tracker.getPortfolioHistory();
      const lastEntry = originalHistory[originalHistory.length - 1];
      
      expect(result).toEqual(lastEntry);
      expect(result).not.toBe(lastEntry); // Different object references
    });
  });

  describe('getMostRecentPayslips', () => {
    it('should return empty array when no payslip data exists', () => {
      // Create a tracker with no initial payslip data
      const emptyTracker = new (class extends WealthTracker {
        constructor() {
          super();
          // Clear the initial payslip data for this test
          (this as any).payslipHistory = [];
        }
      })();
      
      const result = emptyTracker.getMostRecentPayslips(3);
      expect(result).toEqual([]);
    });

    it('should return the requested number of recent payslips', () => {
      const result = tracker.getMostRecentPayslips(2);
      expect(result).toHaveLength(2);
      
      // Should be the last 2 entries from the payslip history
      const allPayslips = tracker.getPayslipHistory();
      const expectedPayslips = allPayslips.slice(-2);
      expect(result).toEqual(expectedPayslips);
    });

    it('should return all payslips if requested count exceeds available', () => {
      const allPayslips = tracker.getPayslipHistory();
      const result = tracker.getMostRecentPayslips(1000);
      expect(result).toEqual(allPayslips);
    });

    it('should return copies, not original objects', () => {
      const result = tracker.getMostRecentPayslips(1);
      const originalHistory = tracker.getPayslipHistory();
      const lastEntry = originalHistory[originalHistory.length - 1];
      
      expect(result[0]).toEqual(lastEntry);
      expect(result[0]).not.toBe(lastEntry); // Different object references
    });
  });

  describe('hasSufficientDataForAnalysis', () => {
    it('should return true when both portfolio and payslip data exist', () => {
      expect(tracker.hasSufficientDataForAnalysis()).toBe(true);
    });

    it('should return false when no portfolio data exists', () => {
      const emptyPortfolioTracker = new (class extends WealthTracker {
        constructor() {
          super();
          (this as any).portfolioHistory = [];
        }
      })();
      
      expect(emptyPortfolioTracker.hasSufficientDataForAnalysis()).toBe(false);
    });

    it('should return false when no payslip data exists', () => {
      const emptyPayslipTracker = new (class extends WealthTracker {
        constructor() {
          super();
          (this as any).payslipHistory = [];
        }
      })();
      
      expect(emptyPayslipTracker.hasSufficientDataForAnalysis()).toBe(false);
    });

    it('should return false when neither portfolio nor payslip data exists', () => {
      const emptyTracker = new (class extends WealthTracker {
        constructor() {
          super();
          (this as any).portfolioHistory = [];
          (this as any).payslipHistory = [];
        }
      })();
      
      expect(emptyTracker.hasSufficientDataForAnalysis()).toBe(false);
    });
  });

  describe('addPortfolioDataIfComplete', () => {
    beforeEach(() => {
      // Spy on console.log to verify messages
      jest.spyOn(console, 'log').mockImplementation(() => {});
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should add portfolio data when all required fields are provided', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      const result = tracker.addPortfolioDataIfComplete(2025, 8, 280000, 120000, 370000, 36000, 5000);
      
      expect(result).toBe(true);
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount + 1);
      
      const newEntry = tracker.getMostRecentPortfolioEntry();
      expect(newEntry?.date).toBe('2025-08');
      expect(newEntry?.trow).toBe(280000);
      expect(newEntry?.robinhood).toBe(120000);
      expect(newEntry?.etrade).toBe(370000);
      expect(newEntry?.teradata).toBe(36000);
      expect(newEntry?.fidelity).toBe(5000);
    });

    it('should not add portfolio data when year is undefined', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      const result = tracker.addPortfolioDataIfComplete(undefined, 8, 280000, 120000, 370000, 36000);
      
      expect(result).toBe(false);
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount);
      expect(console.log).toHaveBeenCalledWith('⏭️  Skipping portfolio data addition - incomplete or invalid input provided');
    });

    it('should not add portfolio data when month is undefined', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      const result = tracker.addPortfolioDataIfComplete(2025, undefined, 280000, 120000, 370000, 36000);
      
      expect(result).toBe(false);
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount);
    });

    it('should not add portfolio data when required portfolio values are undefined', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      
      // Test each required field
      expect(tracker.addPortfolioDataIfComplete(2025, 8, undefined, 120000, 370000, 36000)).toBe(false);
      expect(tracker.addPortfolioDataIfComplete(2025, 8, 280000, undefined, 370000, 36000)).toBe(false);
      expect(tracker.addPortfolioDataIfComplete(2025, 8, 280000, 120000, undefined, 36000)).toBe(false);
      expect(tracker.addPortfolioDataIfComplete(2025, 8, 280000, 120000, 370000, undefined)).toBe(false);
      
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount);
    });

    it('should not add portfolio data when values are NaN', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      const result = tracker.addPortfolioDataIfComplete(2025, 8, NaN, 120000, 370000, 36000);
      
      expect(result).toBe(false);
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount);
    });

    it('should not add portfolio data when year is out of valid range', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      
      expect(tracker.addPortfolioDataIfComplete(2019, 8, 280000, 120000, 370000, 36000)).toBe(false);
      expect(tracker.addPortfolioDataIfComplete(2031, 8, 280000, 120000, 370000, 36000)).toBe(false);
      
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount);
      expect(console.log).toHaveBeenCalledWith('⏭️  Skipping portfolio data addition - invalid year or month range');
    });

    it('should not add portfolio data when month is out of valid range', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      
      expect(tracker.addPortfolioDataIfComplete(2025, 0, 280000, 120000, 370000, 36000)).toBe(false);
      expect(tracker.addPortfolioDataIfComplete(2025, 13, 280000, 120000, 370000, 36000)).toBe(false);
      
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount);
    });

    it('should handle fidelity as optional (undefined should default to 0)', () => {
      const initialCount = tracker.getPortfolioHistory().length;
      const result = tracker.addPortfolioDataIfComplete(2025, 8, 280000, 120000, 370000, 36000, undefined);
      
      expect(result).toBe(true);
      expect(tracker.getPortfolioHistory()).toHaveLength(initialCount + 1);
      
      const newEntry = tracker.getMostRecentPortfolioEntry();
      expect(newEntry?.fidelity).toBe(0);
    });
  });

  describe('getPayslipHistory and getPortfolioHistory', () => {
    it('should return copies of the data arrays', () => {
      const portfolioHistory = tracker.getPortfolioHistory();
      const payslipHistory = tracker.getPayslipHistory();
      
      // Verify they are not the same object references
      expect(portfolioHistory).not.toBe((tracker as any).portfolioHistory);
      expect(payslipHistory).not.toBe((tracker as any).payslipHistory);
      
      // But should have the same content
      expect(portfolioHistory).toEqual((tracker as any).portfolioHistory);
      expect(payslipHistory).toEqual((tracker as any).payslipHistory);
    });

    it('should prevent external modification of internal data', () => {
      const portfolioHistory = tracker.getPortfolioHistory();
      const originalLength = portfolioHistory.length;
      
      // Try to modify the returned array
      portfolioHistory.push({
        date: '2025-12',
        trow: 999999,
        robinhood: 999999,
        etrade: 999999,
        teradata: 999999
      });
      
      // Internal data should remain unchanged
      expect(tracker.getPortfolioHistory()).toHaveLength(originalLength);
    });
  });
});
