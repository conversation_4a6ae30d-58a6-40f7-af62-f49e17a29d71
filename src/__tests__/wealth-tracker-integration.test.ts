import { WealthTracker } from '../wealth-tracker';
import * as formulas from '../formulas';
import { expectedTestData } from './data/expected';

describe('WealthTracker Formula Integration Tests', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    tracker = new WealthTracker();
    // Add some test data
    tracker.addPortfolioData(2024, 1, 200000, 75000, 250000, 32000);
    tracker.addPortfolioData(2024, 7, 270000, 115000, 366000, 35000);
  });

  describe('Immediate Integration: Replace Duplicate Methods', () => {
    describe('IRR Calculation Integration', () => {
      it('should use formulas.calculatePortfolioIRR instead of custom calculateIrr', () => {
        // This test will fail initially because WealthTracker uses custom IRR
        const trackerIrr = tracker.calculateIrr();
        
        // Get portfolio values for formula comparison
        const portfolioValues = tracker.getPortfolioHistory().map(entry => 
          entry.trow + entry.robinhood + entry.etrade + entry.teradata + (entry.fidelity || 0)
        );
        const monthlyContribution = tracker.calculateAverageMonthlyContribution();
        
        const formulaIrr = formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
        
        // Should be the same when using the same formula
        expect(trackerIrr).toBeCloseTo(formulaIrr, 4);
      });

      it('should handle edge cases consistently with formulas', () => {
        // Test with minimal data - both should return 0 for insufficient data
        const minimalTracker = new WealthTracker();
        minimalTracker.addPortfolioData(2024, 1, 100000, 0, 0, 0);

        const trackerIrr = minimalTracker.calculateIrr();

        // With only one data point, IRR should be 0 or a default value
        // Both implementations should handle this consistently
        expect(trackerIrr).toBeGreaterThanOrEqual(0);
        expect(trackerIrr).toBeLessThan(1000); // Reasonable upper bound
      });
    });

    describe('Future Projection Integration', () => {
      it('should use formulas.monthsToTarget instead of custom projectFuture', () => {
        const target = 1000000;
        const monthlyRate = 0.02;
        const monthlyContribution = 6250;
        
        const trackerMonths = tracker.projectFuture(target, monthlyRate, monthlyContribution);
        
        // Get current portfolio value
        const currentValue = tracker.getPortfolioHistory()
          .slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade + 
                  currentValue.teradata + (currentValue.fidelity || 0);
        
        const formulaMonths = formulas.monthsToTarget(pv, target, monthlyRate, monthlyContribution);
        
        expect(trackerMonths).toBe(formulaMonths);
      });

      it('should handle zero rate scenarios consistently', () => {
        const target = 1000000;
        const monthlyRate = 0;
        const monthlyContribution = 6250;
        
        const trackerMonths = tracker.projectFuture(target, monthlyRate, monthlyContribution);
        
        const currentValue = tracker.getPortfolioHistory().slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade + 
                  currentValue.teradata + (currentValue.fidelity || 0);
        
        const formulaMonths = formulas.monthsToTarget(pv, target, monthlyRate, monthlyContribution);
        
        expect(trackerMonths).toBe(formulaMonths);
      });
    });

    describe('Statistics Integration', () => {
      it('should use formulas.calculateStatistics instead of custom implementation', () => {
        const trackerStats = tracker.calculateStatistics();
        
        // Get period returns for formula comparison
        const returns = tracker.calculatePeriodReturns();
        const formulaStats = formulas.calculateStatistics(returns);
        
        expect(trackerStats.meanReturn).toBeCloseTo(formulaStats.mean, 6);
        expect(trackerStats.stdDev).toBeCloseTo(formulaStats.stdDev, 6);
      });
    });
  });

  describe('Short-term Integration: Add Missing Formula Usage', () => {
    describe('Required Contribution Analysis (F-6)', () => {
      it('should calculate required contribution for specific targets and timeframes', () => {
        // This will fail initially as this functionality doesn't exist
        const analysis = tracker.analyzeRequiredContributions();
        
        expect(analysis).toHaveProperty('targets');
        expect(analysis.targets).toHaveLength(3); // 1M, 1.5M, 2M
        
        analysis.targets.forEach(target => {
          expect(target).toHaveProperty('amount');
          expect(target).toHaveProperty('timeframes');
          expect(target.timeframes).toHaveProperty('12months');
          expect(target.timeframes).toHaveProperty('24months');
          expect(target.timeframes).toHaveProperty('36months');
        });
      });

      it('should use formulas.requiredContributionForTarget for calculations', () => {
        // Get actual current value and IRR from tracker
        const currentValue = tracker.getPortfolioHistory().slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade +
                  currentValue.teradata + (currentValue.fidelity || 0);
        const target = 1000000;
        const rate = tracker.calculateIrr();
        const months = 12;

        const expectedPmt = formulas.requiredContributionForTarget(pv, target, rate, months);

        const analysis = tracker.analyzeRequiredContributions();
        const targetAnalysis = analysis.targets.find(t => t.amount === 1000000);

        expect(targetAnalysis).toBeDefined();
        expect(targetAnalysis!.timeframes['12months'].requiredPmt).toBeCloseTo(expectedPmt, 2);
      });
    });

    describe('Component Split Analysis (F-8)', () => {
      it('should provide detailed breakdown of future value components', () => {
        // This will fail initially as this functionality doesn't exist
        const analysis = tracker.analyzeComponentSplit();
        
        expect(analysis).toHaveProperty('projections');
        expect(analysis.projections).toHaveLength(3); // Conservative, Baseline, Optimistic
        
        analysis.projections.forEach(projection => {
          expect(projection).toHaveProperty('scenario');
          expect(projection).toHaveProperty('FV_total');
          expect(projection).toHaveProperty('FV_from_PV');
          expect(projection).toHaveProperty('FV_from_contr');
          expect(projection).toHaveProperty('share_contr');
        });
      });

      it('should use formulas.componentSplit for calculations', () => {
        // Get actual values from tracker
        const currentValue = tracker.getPortfolioHistory().slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade +
                  currentValue.teradata + (currentValue.fidelity || 0);
        const r = tracker.calculateIrr();
        const pmt = tracker.calculateAverageMonthlyContribution();
        const n = 24;

        // Calculate scenario rates to get baseline
        const {meanReturn, stdDev} = tracker.calculateStatistics();
        const scenarios = formulas.calculateScenarioRates(r, meanReturn, stdDev);
        const expectedSplit = formulas.componentSplit(pv, scenarios.baseline, pmt, n);

        const analysis = tracker.analyzeComponentSplit();
        const baselineProjection = analysis.projections.find(p => p.scenario === 'Baseline');

        expect(baselineProjection).toBeDefined();
        expect(baselineProjection!.FV_total).toBe(expectedSplit.FV_total);
        expect(baselineProjection!.FV_from_PV).toBe(expectedSplit.FV_from_PV);
        expect(baselineProjection!.FV_from_contr).toBe(expectedSplit.FV_from_contr);
        expect(baselineProjection!.share_contr).toBe(expectedSplit.share_contr);
      });
    });

    describe('Coast-FIRE Analysis (F-9)', () => {
      it('should calculate coast-FIRE threshold for different scenarios', () => {
        // This will fail initially as this functionality doesn't exist
        const analysis = tracker.analyzeCoastFire();
        
        expect(analysis).toHaveProperty('thresholds');
        expect(analysis.thresholds).toHaveProperty('conservative');
        expect(analysis.thresholds).toHaveProperty('baseline');
        expect(analysis.thresholds).toHaveProperty('optimistic');
        expect(analysis).toHaveProperty('currentStatus');
        expect(analysis).toHaveProperty('monthsToCoastFire');
      });

      it('should use formulas.coastFireThreshold for calculations', () => {
        // Get actual values from tracker
        const monthlyContribution = tracker.calculateAverageMonthlyContribution();
        const irr = tracker.calculateIrr();
        const {meanReturn, stdDev} = tracker.calculateStatistics();
        const scenarios = formulas.calculateScenarioRates(irr, meanReturn, stdDev);

        const expectedThreshold = formulas.coastFireThreshold(scenarios.baseline, monthlyContribution);

        const analysis = tracker.analyzeCoastFire();

        expect(analysis.thresholds.baseline).toBe(expectedThreshold);
      });

      it('should determine if already at coast-FIRE', () => {
        const analysis = tracker.analyzeCoastFire();
        
        expect(analysis.currentStatus).toMatch(/^(Achieved|Not Achieved)$/);
        
        if (analysis.currentStatus === 'Achieved') {
          expect(analysis.monthsToCoastFire).toBe(0);
        } else {
          expect(analysis.monthsToCoastFire).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('Long-term Integration: WealthCalculator Service', () => {
    describe('Service Creation and Integration', () => {
      it('should have a WealthCalculator service that orchestrates formula usage', () => {
        // This will fail initially as WealthCalculator doesn't exist
        const calculator = tracker.getWealthCalculator();

        expect(calculator).toBeDefined();
        expect(calculator).toHaveProperty('calculateComprehensiveAnalysis');
        expect(calculator).toHaveProperty('projectScenarios');
        expect(calculator).toHaveProperty('optimizeContributions');
      });

      it('should provide comprehensive analysis using all formulas', () => {
        const calculator = tracker.getWealthCalculator();
        const analysis = calculator.calculateComprehensiveAnalysis();

        expect(analysis).toHaveProperty('currentStatus');
        expect(analysis).toHaveProperty('performance');
        expect(analysis).toHaveProperty('projections');
        expect(analysis).toHaveProperty('optimization');
        expect(analysis).toHaveProperty('coastFire');
        expect(analysis).toHaveProperty('scenarios');
      });

      it('should project multiple scenarios efficiently', () => {
        const calculator = tracker.getWealthCalculator();
        const scenarios = calculator.projectScenarios([1000000, 1500000, 2000000]);

        expect(scenarios).toHaveLength(3);
        scenarios.forEach(scenario => {
          expect(scenario).toHaveProperty('target');
          expect(scenario).toHaveProperty('conservative');
          expect(scenario).toHaveProperty('baseline');
          expect(scenario).toHaveProperty('optimistic');

          ['conservative', 'baseline', 'optimistic'].forEach(rate => {
            const projection = scenario[rate as keyof typeof scenario];
            expect(projection).toHaveProperty('months');
            expect(projection).toHaveProperty('date');
            expect(projection).toHaveProperty('futureValue');
            expect(projection).toHaveProperty('componentSplit');
          });
        });
      });

      it('should optimize contributions for different goals', () => {
        const calculator = tracker.getWealthCalculator();
        const optimization = calculator.optimizeContributions();

        expect(optimization).toHaveProperty('currentContribution');
        expect(optimization).toHaveProperty('recommendations');
        expect(optimization.recommendations).toHaveLength(9); // 3 targets × 3 timeframes

        optimization.recommendations.forEach(rec => {
          expect(rec).toHaveProperty('target');
          expect(rec).toHaveProperty('timeframe');
          expect(rec).toHaveProperty('requiredContribution');
          expect(rec).toHaveProperty('additionalNeeded');
          expect(rec).toHaveProperty('feasibility');
        });
      });
    });

    describe('Performance and Efficiency', () => {
      it('should cache calculations to avoid redundant formula calls', () => {
        const calculator = tracker.getWealthCalculator();

        // First call
        const start1 = Date.now();
        const analysis1 = calculator.calculateComprehensiveAnalysis();
        const time1 = Date.now() - start1;

        // Second call (should be faster due to caching)
        const start2 = Date.now();
        const analysis2 = calculator.calculateComprehensiveAnalysis();
        const time2 = Date.now() - start2;

        expect(analysis1).toEqual(analysis2);
        expect(time2).toBeLessThan(time1 * 0.5); // Should be at least 50% faster
      });

      it('should invalidate cache when portfolio data changes', () => {
        const calculator = tracker.getWealthCalculator();

        const analysis1 = calculator.calculateComprehensiveAnalysis();

        // Add new portfolio data
        tracker.addPortfolioData(2024, 12, 300000, 120000, 400000, 40000);

        const analysis2 = calculator.calculateComprehensiveAnalysis();

        // Results should be different
        expect(analysis1.currentStatus.totalValue).not.toBe(analysis2.currentStatus.totalValue);
      });
    });

    describe('Integration with Existing Methods', () => {
      it('should enhance analyzeAndProject with comprehensive formula usage', () => {
        // Mock console.log to capture output
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        tracker.analyzeAndProject();

        const output = consoleSpy.mock.calls.map(call => call[0]).join('\n');

        // Should include new analysis sections
        expect(output).toContain('REQUIRED CONTRIBUTIONS ANALYSIS');
        expect(output).toContain('COMPONENT SPLIT ANALYSIS');
        expect(output).toContain('COAST-FIRE ANALYSIS');
        expect(output).toContain('OPTIMIZATION RECOMMENDATIONS');

        consoleSpy.mockRestore();
      });

      it('should maintain backward compatibility with existing output', () => {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        tracker.analyzeAndProject();

        const output = consoleSpy.mock.calls.map(call => call[0]).join('\n');

        // Should still include existing sections
        expect(output).toContain('CURRENT STATUS');
        expect(output).toContain('PERFORMANCE METRICS');
        expect(output).toContain('WEALTH MILESTONES');
        expect(output).toContain('PAYSLIP ANALYSIS');

        consoleSpy.mockRestore();
      });
    });
  });
});
