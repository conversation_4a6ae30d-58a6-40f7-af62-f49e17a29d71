import * as formulas from '../formulas';

describe('New Financial Formulas', () => {
  const testData = {
    start_balance: 786000,
    monthly_growth_rate: 0.0239,
    test_cases: [
      // $1,000,000 target
      { PMT: 6250, FV: 1000000, expect_months: 8 },
      { PMT: 5260, FV: 1000000, expect_months: 9 },
      { PMT: 4200, FV: 1000000, expect_months: 9 },

      // $1,500,000 target
      { PMT: 6250, FV: 1500000, expect_months: 23 },
      { PMT: 5260, FV: 1500000, expect_months: 23 },
      { PMT: 4200, FV: 1500000, expect_months: 24 },

      // $2,000,000 target
      { PMT: 6250, FV: 2000000, expect_months: 33 },
      { PMT: 5260, FV: 2000000, expect_months: 34 },
      { PMT: 4200, FV: 2000000, expect_months: 35 }
    ]
  };

  describe('D-3: Months to Target - All Expected Values', () => {
    testData.test_cases.forEach((testCase, index) => {
      it(`should calculate ${testCase.expect_months} months for PMT=${testCase.PMT} to reach $${testCase.FV.toLocaleString()}`, () => {
        const months = formulas.monthsToTarget(
          testData.start_balance,
          testCase.FV,
          testData.monthly_growth_rate,
          testCase.PMT
        );
        
        expect(months).toBe(testCase.expect_months);
      });
    });
  });

  describe('F-6: Required Contribution for Target', () => {
    it('should calculate required PMT for $2M in 36 months', () => {
      const pmt = formulas.requiredContributionForTarget(
        testData.start_balance,
        2000000,
        testData.monthly_growth_rate,
        36
      );
      
      expect(pmt).toBeCloseTo(2862.44, 2);
    });
  });

  describe('F-8: Component Split', () => {
    it('should calculate component split for 23 months with $6250 PMT', () => {
      const result = formulas.componentSplit(
        testData.start_balance,
        testData.monthly_growth_rate,
        6250,
        23
      );
      
      expect(result.FV_total).toBeCloseTo(1541844, -2);
      expect(result.FV_from_PV).toBeCloseTo(1353150, -2);
      expect(result.FV_from_contr).toBeCloseTo(188694, -2);
      expect(result.share_contr).toBeCloseTo(0.122, 3);
    });
  });

  describe('F-9: Coast-FIRE Threshold', () => {
    it('should calculate coast-FIRE threshold for $6250 monthly contribution', () => {
      const threshold = formulas.coastFireThreshold(
        testData.monthly_growth_rate,
        6250
      );
      
      expect(threshold).toBeCloseTo(228888, -2);
    });
  });

  describe('Cross-validation Tests', () => {
    it('should validate that D-1 and D-3 formulas are inverses', () => {
      const pv = testData.start_balance;
      const target = 1000000;
      const r = testData.monthly_growth_rate;
      const pmt = 6250;
      
      const months = formulas.monthsToTarget(pv, target, r, pmt);
      const calculatedFV = formulas.futureValueWithContributions(pv, r, pmt, months);
      
      expect(calculatedFV).toBeGreaterThanOrEqual(target);
      expect(calculatedFV).toBeLessThan(target * 1.05); // Within 5% (due to rounding up months)
    });

    it('should validate that F-6 produces correct PMT for exact target', () => {
      const pv = testData.start_balance;
      const target = 2000000;
      const r = testData.monthly_growth_rate;
      const months = 36;
      
      const requiredPMT = formulas.requiredContributionForTarget(pv, target, r, months);
      const achievedFV = formulas.futureValueWithContributions(pv, r, requiredPMT, months);
      
      expect(achievedFV).toBeCloseTo(target, -1); // Within $10
    });
  });
});
