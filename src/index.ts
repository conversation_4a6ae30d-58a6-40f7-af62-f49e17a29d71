import {PayslipParser} from "./payslip-parser";
import {WealthTracker} from "./wealth-tracker";
import * as readline from "node:readline";

async function main(): Promise<void> {
  console.log('main: Starting application');
  const tracker = new WealthTracker();
  console.log('main: WealthTracker initialized');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  console.log('main: Readline interface created');

  const question = (query: string): Promise<string> => {
    console.log(`question: Asking query: ${query}`);
    return new Promise(resolve => rl.question(query, resolve));
  };

  const multilineQuestion = (query: string): Promise<string> => {
    console.log(`multilineQuestion: Asking query: ${query}`);
    return new Promise(resolve => {
      console.log(query);
      console.log('(Press Enter twice when done)');
      
      let lines: string[] = [];
      let emptyLineCount = 0;
      
      const onLine = (line: string) => {
        console.log(`multilineQuestion: Received line: ${line}`);
        if (line.trim() === '') {
          emptyLineCount++;
          console.log(`multilineQuestion: Empty line count: ${emptyLineCount}`);
          if (emptyLineCount >= 2) {
            rl.off('line', onLine);
            console.log(`multilineQuestion: Two empty lines, resolving with ${lines.length} lines`);
            resolve(lines.join('\n'));
            return;
          }
        } else {
          emptyLineCount = 0;
        }
        lines.push(line);
      };
      
      rl.on('line', onLine);
    });
  };

  console.log('\n' + '='.repeat(60));
  console.log('PORTFOLIO WEALTH TRACKER');
  console.log('='.repeat(60));
  console.log('Let\'s update your wealth data step by step!');
  console.log('💡 Tip: Leave any field empty to skip and use existing data');

  // Load existing data first
  tracker.loadData();

  try {
    // Step 1: Get month and year (optional)
    const yearInput = await question('\nStep 1: Enter year (e.g., 2025) or press Enter to skip: ');
    console.log(`main: Year input: "${yearInput}"`);
    const monthInput = await question('Step 2: Enter month (1-12) or press Enter to skip: ');
    console.log(`main: Month input: "${monthInput}"`);

    const year = yearInput.trim() ? parseInt(yearInput) : undefined;
    const month = monthInput.trim() ? parseInt(monthInput) : undefined;
    console.log(`main: Parsed year: ${year}, month: ${month}`);
    
    // Step 2: Get payslips
    console.log('\nStep 3: Please paste your previous 3 payslips (one at a time)');
    
    const payslipTexts: string[] = [];
    for (let i = 1; i <= 3; i++) {
      const payslipText = await multilineQuestion(`\nPaste payslip ${i}:`);
      if (payslipText.trim()) {
        payslipTexts.push(payslipText);
        console.log(`main: Payslip ${i} added`);
      }
    }
    
    // Parse payslips
    if (payslipTexts.length > 0) {
      console.log('main: Parsing payslips...');
      const parsedPayslips = PayslipParser.parseMultiplePayslips(payslipTexts);
      console.log(`main: Parsed ${parsedPayslips.length} payslips`);
      
      for (const payslip of parsedPayslips) {
        if (PayslipParser.validatePayslipData(payslip)) {
          tracker.addPayslip(payslip.date, payslip.gross, payslip.net, payslip.espp, payslip.roth_e, payslip.roth_r);
          console.log(`main: Payslip added to tracker: ${payslip.date}`);
        } else {
          console.log(`main: Invalid payslip data, skipping: ${payslip.date}`);
        }
      }
      
      console.log(`✅ Successfully parsed ${parsedPayslips.length} payslips`);
    }
    
    // Step 3: Get portfolio data
    console.log('\nStep 4: Enter current portfolio values for this month:');
    const trow = parseFloat(await question('T.Rowe Retirement: '));
    console.log(`main: T.Rowe Retirement: ${trow}`);
    const robinhood = parseFloat(await question('RobinHood: '));
    console.log(`main: RobinHood: ${robinhood}`);
    const etrade = parseFloat(await question('E*Trade: '));
    console.log(`main: E*Trade: ${etrade}`);
    const teradata = parseFloat(await question('Teradata 401k: '));
    console.log(`main: Teradata 401k: ${teradata}`);
    const fidelity = parseFloat(await question('Fidelity (or 0 if none): ')) || 0;
    console.log(`main: Fidelity: ${fidelity}`);
    
    tracker.addPortfolioData(year, month, trow, robinhood, etrade, teradata, fidelity);
    console.log('main: Portfolio data added to tracker');
    
    // Step 4: Show analysis
    console.log('\nStep 5: Analysis and projections:');
    tracker.analyzeAndProject();
    console.log('main: Analysis and projection complete');
    
    // Save data
    tracker.saveData();
    console.log('\n✅ All data has been saved!');
    
  } catch (error) {
    console.log('\n❌ An error occurred:', error);
  }
  
  rl.close();
  console.log('main: Readline interface closed');
}

if (require.main === module) {
  main().catch(error => console.error('main: Unhandled error in main function:', error));
}
